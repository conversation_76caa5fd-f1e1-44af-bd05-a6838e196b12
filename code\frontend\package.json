{"name": "frontend", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.1", "@tanstack/react-query": "^5.62.7", "axios": "^1.7.9", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/react-native": "^13.2.2", "@types/react": "~19.0.10", "babel-jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "react-native-web": "^0.21.0", "typescript": "~5.8.3"}, "private": true}