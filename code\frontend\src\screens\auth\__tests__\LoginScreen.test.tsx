/**
 * LoginScreen Test Suite
 * Comprehensive tests for login functionality, validation, and user interactions
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useMutation } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { LoginScreen } from '../LoginScreen';

// Mock the navigation prop
const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

// Mock useMutation
const mockMutate = jest.fn();
const mockUseMutation = useMutation as jest.MockedFunction<typeof useMutation>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('LoginScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
      data: null,
      isSuccess: false,
      reset: jest.fn(),
    } as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders all essential UI elements', () => {
      render(<LoginScreen navigation={mockNavigation} />);

      expect(screen.getByText('Welcome Back')).toBeTruthy();
      expect(screen.getByText('Sign in to your Vierla account')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your email')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your password')).toBeTruthy();
      expect(screen.getByText('Sign In')).toBeTruthy();
      expect(screen.getByText('Forgot Password?')).toBeTruthy();
      expect(screen.getByText('or continue with')).toBeTruthy();
      expect(screen.getByText("Don't have an account?")).toBeTruthy();
    });

    it('renders social authentication buttons', () => {
      render(<LoginScreen navigation={mockNavigation} />);

      // Social buttons should be present (testing by their container)
      const socialButtonsContainer = screen.getByTestId('social-buttons-container');
      expect(socialButtonsContainer).toBeTruthy();
    });
  });

  describe('Form Validation', () => {
    it('shows validation errors for empty fields', async () => {
      render(<LoginScreen navigation={mockNavigation} />);

      const signInButton = screen.getByText('Sign In');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeTruthy();
        expect(screen.getByText('Password is required')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('shows validation error for invalid email format', async () => {
      render(<LoginScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('passes validation with valid email and password', async () => {
      render(<LoginScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });
  });

  describe('Authentication Flow', () => {
    it('handles successful login', async () => {
      const mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2023-01-01T00:00:00Z',
        },
      };

      // Mock successful mutation
      mockUseMutation.mockReturnValue({
        mutate: jest.fn((data, { onSuccess }) => {
          onSuccess(mockAuthResponse);
        }),
        isPending: false,
        isError: false,
        error: null,
        data: mockAuthResponse,
        isSuccess: true,
        reset: jest.fn(),
      } as any);

      render(<LoginScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(AsyncStorage.multiSet).toHaveBeenCalledWith([
          ['access_token', 'mock-access-token'],
          ['refresh_token', 'mock-refresh-token'],
          ['user', JSON.stringify(mockAuthResponse.user)],
        ]);
        expect(mockNavigation.replace).toHaveBeenCalledWith('Main');
      });
    });

    it('handles login failure with invalid credentials', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Invalid credentials' },
        },
      };

      // Mock failed mutation
      mockUseMutation.mockReturnValue({
        mutate: jest.fn((data, { onError }) => {
          onError(mockError);
        }),
        isPending: false,
        isError: true,
        error: mockError,
        data: null,
        isSuccess: false,
        reset: jest.fn(),
      } as any);

      render(<LoginScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'wrongpassword');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Login Failed', 'Invalid credentials');
      });
    });

    it('handles account locked error', async () => {
      const mockError = {
        response: {
          status: 423,
          data: { detail: 'Account locked' },
        },
      };

      // Mock failed mutation with account locked
      mockUseMutation.mockReturnValue({
        mutate: jest.fn((data, { onError }) => {
          onError(mockError);
        }),
        isPending: false,
        isError: true,
        error: mockError,
        data: null,
        isSuccess: false,
        reset: jest.fn(),
      } as any);

      render(<LoginScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Account Locked',
          'Your account is temporarily locked due to multiple failed login attempts.'
        );
      });
    });
  });

  describe('Navigation', () => {
    it('navigates to forgot password screen', () => {
      render(<LoginScreen navigation={mockNavigation} />);

      const forgotPasswordButton = screen.getByText('Forgot Password?');
      fireEvent.press(forgotPasswordButton);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('ForgotPassword');
    });

    it('navigates to register screen', () => {
      render(<LoginScreen navigation={mockNavigation} />);

      const signUpText = screen.getByText('Sign Up');
      fireEvent.press(signUpText);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Register');
    });
  });

  describe('Social Authentication', () => {
    it('shows placeholder for Google Sign-In', async () => {
      render(<LoginScreen navigation={mockNavigation} />);

      // Find and press Google sign-in button (assuming it has a testID)
      const googleButton = screen.getByTestId('google-signin-button');
      fireEvent.press(googleButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Google Sign-In',
          'Google Sign-In integration will be implemented in the next phase.',
          [{ text: 'OK', onPress: expect.any(Function) }]
        );
      });
    });

    it('shows placeholder for Apple Sign-In', async () => {
      render(<LoginScreen navigation={mockNavigation} />);

      // Find and press Apple sign-in button (assuming it has a testID)
      const appleButton = screen.getByTestId('apple-signin-button');
      fireEvent.press(appleButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Apple Sign-In',
          'Apple Sign-In integration will be implemented in the next phase.',
          [{ text: 'OK', onPress: expect.any(Function) }]
        );
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state during login', () => {
      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        isPending: true,
        isError: false,
        error: null,
        data: null,
        isSuccess: false,
        reset: jest.fn(),
      } as any);

      render(<LoginScreen navigation={mockNavigation} />);

      const signInButton = screen.getByText('Sign In');
      expect(signInButton.props.accessibilityState?.busy).toBe(true);
    });
  });
});
