/**
 * Input Component Test Suite
 * Tests for the reusable Input component
 */

import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { Input } from '../Input';

describe('Input Component', () => {
  describe('Rendering', () => {
    it('renders with label', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
        />
      );
      
      expect(screen.getByText('Email')).toBeTruthy();
    });

    it('renders with placeholder', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter your email address"
        />
      );
      
      expect(screen.getByPlaceholderText('Enter your email address')).toBeTruthy();
    });

    it('renders with value', () => {
      render(
        <Input 
          label="Email" 
          value="<EMAIL>" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
        />
      );
      
      const input = screen.getByDisplayValue('<EMAIL>');
      expect(input).toBeTruthy();
    });

    it('renders without label when not provided', () => {
      render(
        <Input 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter text"
        />
      );
      
      expect(screen.getByPlaceholderText('Enter text')).toBeTruthy();
    });
  });

  describe('Interaction', () => {
    it('calls onChangeText when text changes', () => {
      const mockOnChangeText = jest.fn();
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={mockOnChangeText} 
          placeholder="Enter email"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      fireEvent.changeText(input, '<EMAIL>');
      
      expect(mockOnChangeText).toHaveBeenCalledWith('<EMAIL>');
    });

    it('calls onFocus when input is focused', () => {
      const mockOnFocus = jest.fn();
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          onFocus={mockOnFocus}
          placeholder="Enter email"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      fireEvent(input, 'focus');
      
      expect(mockOnFocus).toHaveBeenCalled();
    });

    it('calls onBlur when input loses focus', () => {
      const mockOnBlur = jest.fn();
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          onBlur={mockOnBlur}
          placeholder="Enter email"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      fireEvent(input, 'blur');
      
      expect(mockOnBlur).toHaveBeenCalled();
    });
  });

  describe('Error State', () => {
    it('displays error message when error prop is provided', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          error="Please enter a valid email address"
        />
      );
      
      expect(screen.getByText('Please enter a valid email address')).toBeTruthy();
    });

    it('applies error styles when error is present', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          error="Invalid email"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            borderColor: expect.any(String),
          }),
        ])
      );
    });

    it('does not display error message when error prop is not provided', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
        />
      );
      
      expect(screen.queryByText('Invalid email')).toBeNull();
    });
  });

  describe('Disabled State', () => {
    it('disables input when disabled prop is true', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          disabled
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.editable).toBe(false);
    });

    it('applies disabled styles when disabled', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          disabled
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            opacity: expect.any(Number),
          }),
        ])
      );
    });
  });

  describe('Secure Text Entry', () => {
    it('hides text when secureTextEntry is true', () => {
      render(
        <Input 
          label="Password" 
          value="password123" 
          onChangeText={jest.fn()} 
          placeholder="Enter password"
          secureTextEntry
        />
      );
      
      const input = screen.getByPlaceholderText('Enter password');
      expect(input.props.secureTextEntry).toBe(true);
    });

    it('shows text when secureTextEntry is false', () => {
      render(
        <Input 
          label="Email" 
          value="<EMAIL>" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          secureTextEntry={false}
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.secureTextEntry).toBe(false);
    });
  });

  describe('Keyboard Type', () => {
    it('sets email keyboard type', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          keyboardType="email-address"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.keyboardType).toBe('email-address');
    });

    it('sets numeric keyboard type', () => {
      render(
        <Input 
          label="Phone" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter phone number"
          keyboardType="phone-pad"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter phone number');
      expect(input.props.keyboardType).toBe('phone-pad');
    });
  });

  describe('Auto Capitalization', () => {
    it('sets auto capitalization to none', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          autoCapitalize="none"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.autoCapitalize).toBe('none');
    });

    it('sets auto capitalization to words', () => {
      render(
        <Input 
          label="Name" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter your name"
          autoCapitalize="words"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter your name');
      expect(input.props.autoCapitalize).toBe('words');
    });
  });

  describe('Icons', () => {
    it('renders with left icon', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
          leftIcon="mail"
        />
      );
      
      expect(screen.getByPlaceholderText('Enter email')).toBeTruthy();
      // Icon should be rendered (testing for its presence in the component tree)
    });

    it('renders with right icon', () => {
      render(
        <Input 
          label="Password" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter password"
          rightIcon="eye"
        />
      );
      
      expect(screen.getByPlaceholderText('Enter password')).toBeTruthy();
      // Icon should be rendered (testing for its presence in the component tree)
    });
  });

  describe('Multiline', () => {
    it('supports multiline input', () => {
      render(
        <Input 
          label="Description" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter description"
          multiline
          numberOfLines={4}
        />
      );
      
      const input = screen.getByPlaceholderText('Enter description');
      expect(input.props.multiline).toBe(true);
      expect(input.props.numberOfLines).toBe(4);
    });
  });

  describe('Accessibility', () => {
    it('has correct accessibility label from label prop', () => {
      render(
        <Input 
          label="Email Address" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter email"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter email');
      expect(input.props.accessibilityLabel).toBe('Email Address');
    });

    it('has correct accessibility hint from placeholder', () => {
      render(
        <Input 
          label="Email" 
          value="" 
          onChangeText={jest.fn()} 
          placeholder="Enter your email address"
        />
      );
      
      const input = screen.getByPlaceholderText('Enter your email address');
      expect(input.props.accessibilityHint).toBe('Enter your email address');
    });
  });
});
