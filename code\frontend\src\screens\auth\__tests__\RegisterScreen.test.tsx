/**
 * RegisterScreen Test Suite
 * Comprehensive tests for user registration functionality
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { useMutation } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { RegisterScreen } from '../RegisterScreen';

// Mock the navigation prop
const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

// Mock useMutation
const mockMutate = jest.fn();
const mockUseMutation = useMutation as jest.MockedFunction<typeof useMutation>;

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('RegisterScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
      data: null,
      isSuccess: false,
      reset: jest.fn(),
    } as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders all essential UI elements', () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      expect(screen.getByText('Create Account')).toBeTruthy();
      expect(screen.getByText('Join Vierla today')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your first name')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your last name')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your email')).toBeTruthy();
      expect(screen.getByPlaceholderText('Enter your username')).toBeTruthy();
      expect(screen.getByPlaceholderText('Create a password')).toBeTruthy();
      expect(screen.getByPlaceholderText('Confirm your password')).toBeTruthy();
      expect(screen.getByText('Create Account')).toBeTruthy();
      expect(screen.getByText('Already have an account?')).toBeTruthy();
    });
  });

  describe('Form Validation', () => {
    it('shows validation errors for empty required fields', async () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const createAccountButton = screen.getByText('Create Account');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('First name is required')).toBeTruthy();
        expect(screen.getByText('Last name is required')).toBeTruthy();
        expect(screen.getByText('Email is required')).toBeTruthy();
        expect(screen.getByText('Username is required')).toBeTruthy();
        expect(screen.getByText('Password is required')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('shows validation error for invalid email format', async () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('shows validation error for weak password', async () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const passwordInput = screen.getByPlaceholderText('Create a password');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(passwordInput, '123');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 8 characters long')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('shows validation error for password mismatch', async () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const passwordInput = screen.getByPlaceholderText('Create a password');
      const confirmPasswordInput = screen.getByPlaceholderText('Confirm your password');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'differentpassword');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Passwords do not match')).toBeTruthy();
      });

      expect(mockMutate).not.toHaveBeenCalled();
    });

    it('passes validation with valid data', async () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const firstNameInput = screen.getByPlaceholderText('Enter your first name');
      const lastNameInput = screen.getByPlaceholderText('Enter your last name');
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const usernameInput = screen.getByPlaceholderText('Enter your username');
      const passwordInput = screen.getByPlaceholderText('Create a password');
      const confirmPasswordInput = screen.getByPlaceholderText('Confirm your password');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(firstNameInput, 'John');
      fireEvent.changeText(lastNameInput, 'Doe');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(usernameInput, 'johndoe');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalledWith({
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          username: 'johndoe',
          password: 'password123',
          password_confirm: 'password123',
        });
      });
    });
  });

  describe('Registration Flow', () => {
    it('handles successful registration', async () => {
      const mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'johndoe',
          first_name: 'John',
          last_name: 'Doe',
          full_name: 'John Doe',
          role: 'customer',
          is_verified: false,
          account_status: 'pending_verification',
          created_at: '2023-01-01T00:00:00Z',
        },
        message: 'Registration successful. Please check your email to verify your account.',
      };

      // Mock successful mutation
      mockUseMutation.mockReturnValue({
        mutate: jest.fn((data, { onSuccess }) => {
          onSuccess(mockAuthResponse);
        }),
        isPending: false,
        isError: false,
        error: null,
        data: mockAuthResponse,
        isSuccess: true,
        reset: jest.fn(),
      } as any);

      render(<RegisterScreen navigation={mockNavigation} />);

      const firstNameInput = screen.getByPlaceholderText('Enter your first name');
      const lastNameInput = screen.getByPlaceholderText('Enter your last name');
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const usernameInput = screen.getByPlaceholderText('Enter your username');
      const passwordInput = screen.getByPlaceholderText('Create a password');
      const confirmPasswordInput = screen.getByPlaceholderText('Confirm your password');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(firstNameInput, 'John');
      fireEvent.changeText(lastNameInput, 'Doe');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(usernameInput, 'johndoe');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(AsyncStorage.multiSet).toHaveBeenCalledWith([
          ['access_token', 'mock-access-token'],
          ['refresh_token', 'mock-refresh-token'],
          ['user', JSON.stringify(mockAuthResponse.user)],
        ]);
        expect(Alert.alert).toHaveBeenCalledWith(
          'Registration Successful',
          'Registration successful. Please check your email to verify your account.'
        );
        expect(mockNavigation.replace).toHaveBeenCalledWith('Main');
      });
    });

    it('handles registration failure with duplicate email', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { email: ['User with this email already exists.'] },
        },
      };

      // Mock failed mutation
      mockUseMutation.mockReturnValue({
        mutate: jest.fn((data, { onError }) => {
          onError(mockError);
        }),
        isPending: false,
        isError: true,
        error: mockError,
        data: null,
        isSuccess: false,
        reset: jest.fn(),
      } as any);

      render(<RegisterScreen navigation={mockNavigation} />);

      const firstNameInput = screen.getByPlaceholderText('Enter your first name');
      const lastNameInput = screen.getByPlaceholderText('Enter your last name');
      const emailInput = screen.getByPlaceholderText('Enter your email');
      const usernameInput = screen.getByPlaceholderText('Enter your username');
      const passwordInput = screen.getByPlaceholderText('Create a password');
      const confirmPasswordInput = screen.getByPlaceholderText('Confirm your password');
      const createAccountButton = screen.getByText('Create Account');

      fireEvent.changeText(firstNameInput, 'John');
      fireEvent.changeText(lastNameInput, 'Doe');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(usernameInput, 'johndoe');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');
      fireEvent.press(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('User with this email already exists.')).toBeTruthy();
      });
    });
  });

  describe('Navigation', () => {
    it('navigates to login screen', () => {
      render(<RegisterScreen navigation={mockNavigation} />);

      const signInText = screen.getByText('Sign In');
      fireEvent.press(signInText);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Login');
    });
  });

  describe('Loading States', () => {
    it('shows loading state during registration', () => {
      mockUseMutation.mockReturnValue({
        mutate: mockMutate,
        isPending: true,
        isError: false,
        error: null,
        data: null,
        isSuccess: false,
        reset: jest.fn(),
      } as any);

      render(<RegisterScreen navigation={mockNavigation} />);

      const createAccountButton = screen.getByText('Create Account');
      expect(createAccountButton.props.accessibilityState?.busy).toBe(true);
    });
  });
});
