/**
 * Button Component Test Suite
 * Tests for the reusable Button component
 */

import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button Component', () => {
  describe('Rendering', () => {
    it('renders with title', () => {
      render(<Button title="Test Button" onPress={jest.fn()} />);
      
      expect(screen.getByText('Test Button')).toBeTruthy();
    });

    it('renders with primary variant by default', () => {
      render(<Button title="Primary Button" onPress={jest.fn()} />);
      
      const button = screen.getByText('Primary Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            backgroundColor: expect.any(String),
          }),
        ])
      );
    });

    it('renders with outline variant', () => {
      render(<Button title="Outline Button" onPress={jest.fn()} variant="outline" />);
      
      const button = screen.getByText('Outline Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            borderWidth: expect.any(Number),
          }),
        ])
      );
    });

    it('renders with text variant', () => {
      render(<Button title="Text Button" onPress={jest.fn()} variant="text" />);
      
      const button = screen.getByText('Text Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            backgroundColor: 'transparent',
          }),
        ])
      );
    });
  });

  describe('Interaction', () => {
    it('calls onPress when pressed', () => {
      const mockOnPress = jest.fn();
      render(<Button title="Clickable Button" onPress={mockOnPress} />);
      
      const button = screen.getByText('Clickable Button');
      fireEvent.press(button);
      
      expect(mockOnPress).toHaveBeenCalledTimes(1);
    });

    it('does not call onPress when disabled', () => {
      const mockOnPress = jest.fn();
      render(<Button title="Disabled Button" onPress={mockOnPress} disabled />);
      
      const button = screen.getByText('Disabled Button');
      fireEvent.press(button);
      
      expect(mockOnPress).not.toHaveBeenCalled();
    });

    it('does not call onPress when loading', () => {
      const mockOnPress = jest.fn();
      render(<Button title="Loading Button" onPress={mockOnPress} loading />);
      
      const button = screen.getByText('Loading Button');
      fireEvent.press(button);
      
      expect(mockOnPress).not.toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('shows loading indicator when loading', () => {
      render(<Button title="Loading Button" onPress={jest.fn()} loading />);
      
      // Check for ActivityIndicator (it should be present)
      const button = screen.getByText('Loading Button').parent;
      expect(button).toBeTruthy();
      
      // The button should have accessibility state indicating it's busy
      expect(button?.props.accessibilityState?.busy).toBe(true);
    });

    it('hides title when loading', () => {
      render(<Button title="Loading Button" onPress={jest.fn()} loading />);
      
      // Title should still be present but might be visually hidden
      expect(screen.getByText('Loading Button')).toBeTruthy();
    });
  });

  describe('Disabled State', () => {
    it('applies disabled styles when disabled', () => {
      render(<Button title="Disabled Button" onPress={jest.fn()} disabled />);
      
      const button = screen.getByText('Disabled Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            opacity: expect.any(Number),
          }),
        ])
      );
    });

    it('has correct accessibility state when disabled', () => {
      render(<Button title="Disabled Button" onPress={jest.fn()} disabled />);
      
      const button = screen.getByText('Disabled Button').parent;
      expect(button?.props.accessibilityState?.disabled).toBe(true);
    });
  });

  describe('Size Variants', () => {
    it('renders with small size', () => {
      render(<Button title="Small Button" onPress={jest.fn()} size="sm" />);
      
      const button = screen.getByText('Small Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            paddingVertical: expect.any(Number),
            paddingHorizontal: expect.any(Number),
          }),
        ])
      );
    });

    it('renders with medium size by default', () => {
      render(<Button title="Medium Button" onPress={jest.fn()} />);
      
      const button = screen.getByText('Medium Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            paddingVertical: expect.any(Number),
            paddingHorizontal: expect.any(Number),
          }),
        ])
      );
    });

    it('renders with large size', () => {
      render(<Button title="Large Button" onPress={jest.fn()} size="lg" />);
      
      const button = screen.getByText('Large Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            paddingVertical: expect.any(Number),
            paddingHorizontal: expect.any(Number),
          }),
        ])
      );
    });
  });

  describe('Custom Styling', () => {
    it('applies custom style prop', () => {
      const customStyle = { marginTop: 20 };
      render(<Button title="Custom Button" onPress={jest.fn()} style={customStyle} />);
      
      const button = screen.getByText('Custom Button').parent;
      expect(button?.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining(customStyle),
        ])
      );
    });
  });

  describe('Accessibility', () => {
    it('has correct accessibility role', () => {
      render(<Button title="Accessible Button" onPress={jest.fn()} />);
      
      const button = screen.getByText('Accessible Button').parent;
      expect(button?.props.accessibilityRole).toBe('button');
    });

    it('has correct accessibility label', () => {
      render(<Button title="Accessible Button" onPress={jest.fn()} />);
      
      const button = screen.getByText('Accessible Button').parent;
      expect(button?.props.accessibilityLabel).toBe('Accessible Button');
    });

    it('supports custom accessibility label', () => {
      render(
        <Button 
          title="Button" 
          onPress={jest.fn()} 
          accessibilityLabel="Custom accessibility label"
        />
      );
      
      const button = screen.getByText('Button').parent;
      expect(button?.props.accessibilityLabel).toBe('Custom accessibility label');
    });
  });

  describe('Icon Support', () => {
    it('renders with left icon', () => {
      render(<Button title="Icon Button" onPress={jest.fn()} leftIcon="add" />);
      
      expect(screen.getByText('Icon Button')).toBeTruthy();
      // Icon should be rendered (testing for its presence in the component tree)
    });

    it('renders with right icon', () => {
      render(<Button title="Icon Button" onPress={jest.fn()} rightIcon="arrow-forward" />);
      
      expect(screen.getByText('Icon Button')).toBeTruthy();
      // Icon should be rendered (testing for its presence in the component tree)
    });

    it('renders with both left and right icons', () => {
      render(
        <Button 
          title="Icon Button" 
          onPress={jest.fn()} 
          leftIcon="add" 
          rightIcon="arrow-forward"
        />
      );
      
      expect(screen.getByText('Icon Button')).toBeTruthy();
      // Both icons should be rendered
    });
  });
});
